from flask import Blueprint, request, jsonify, current_app, send_file
from sqlalchemy import text
from datetime import datetime, timedelta
import pandas as pd
from pear_admin.extensions import db
from pear_admin.orms.ordermanage import OrderSync, StockSync, AllocationPlan, ShopAllocationCount, ShopInfo
import os
import numpy as np

ordermanage_api = Blueprint("ordermanage", __name__, url_prefix="/ordermanage")

def get_mssql_engine():
    """获取MSSQL数据库连接"""
    return db.get_engine(bind='mssql')

def parse_date_range(date_range):
    """解析日期范围字符串"""
    if not date_range:
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        return yesterday, yesterday
        
    try:
        current_app.logger.debug(f"原始日期范围字符串: {date_range}")
        
        # 直接按照 " - " 分割（注意空格）
        if " - " not in date_range:
            raise ValueError(f"日期范围格式不正确，应为: YYYY-MM-DD - YYYY-MM-DD")
            
        date_start, date_end = date_range.split(" - ")
        
        # 验证日期格式
        datetime.strptime(date_start.strip(), '%Y-%m-%d')
        datetime.strptime(date_end.strip(), '%Y-%m-%d')
        
        return date_start.strip(), date_end.strip()
        
    except ValueError as e:
        current_app.logger.error(f"日期解析错误: {str(e)}")
        raise ValueError(f"日期格式不正确，应为: YYYY-MM-DD - YYYY-MM-DD")
    except Exception as e:
        current_app.logger.error(f"未预期的错误: {str(e)}")
        raise ValueError(f"日期处理出错: {str(e)}")

@ordermanage_api.route('/sync_data', methods=['POST'])
def sync_data():
    """同步订单和库存数据"""
    try:
        date_range = request.json.get('date_range')
        current_app.logger.info(f"收到的日期范围参数: {date_range}")  # 添加日志
        
        if not date_range:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            date_range = f"{yesterday} - {yesterday}"
            current_app.logger.info(f"使用默认日期范围: {date_range}")  # 添加日志
            
        try:
            current_app.logger.info(f"开始解析日期范围: {date_range}")  # 添加日志
            date_start, date_end = parse_date_range(date_range)
            current_app.logger.info(f"日期范围解析结果: start={date_start}, end={date_end}")  # 添加日志
        except ValueError as e:
            current_app.logger.error(f"日期范围解析失败: {str(e)}")  # 添加日志
            current_app.logger.error(f"错误类型: {type(e)}")  # 添加日志
            return jsonify({
                'code': 1,
                'msg': str(e)
            })
            
        current_app.logger.info(f"开始同步数据，日期范围：{date_start} 至 {date_end}")
        
        # # 先执行存储过程生成库存数据表
        # current_app.logger.info("开始执行存储过程 PROC_OV_KDPCLSJY...")
        # with get_mssql_engine().connect() as conn:
        #     conn.execute(text("EXEC PROC_OV_KDPCLSJY"))
        # current_app.logger.info("存储过程执行完成")
            
        # 同步客订数据
        current_app.logger.info("开始同步客订数据...")
        order_sql = """
        SELECT v.RQ '日期', v.DJBH '订单号', d.LXMC '订单类型',
               c.CKDM '店铺代码', c.CKMC '店铺', LEFT(kh.PROVINCE,2) '省',
               sp.SPDM '商品代码', g1.GGDM '颜色代码', g1.GGMC '颜色',
               g2.GGDM '尺码代码', g2.GGMC '尺码', SUM(v.SL) '订单量',
               v.BZ '备注1', v.ZY '备注2', sp.byzd8 '年份', sp.BYZD4 '大类代码',
               dlmc '大类', sp.BYZD5 '季节代码', JJMC '季节', sp.FJSX1 '小类代码',
               fjsx1.SXMC '小类', ISNULL(NULLIF(LTRIM(RTRIM(v.bz)), ''), v.zy) AS '备注'
        FROM VW_PHJRDMX v
        LEFT JOIN CANGKU c ON v.DM1=c.CKDM
        LEFT JOIN KUWEI kw ON kw.KWDM=v.DM2_1
        LEFT JOIN SHANGPIN sp ON v.SPDM=sp.SPDM
        LEFT JOIN DDLX d ON v.DM4=d.LXDM
        LEFT JOIN GUIGE1 g1 ON v.GG1DM=g1.GGDM
        LEFT JOIN SPGG1 sg ON v.SPDM=sg.SPDM AND v.GG1DM=sg.GGDM
        LEFT JOIN GUIGE2 g2 ON v.GG2DM=g2.GGDM
        LEFT JOIN KEHU kh ON v.DM1=kh.KHDM
		LEFT JOIN DALEI ON sp.byzd4=DLDM
		LEFT JOIN JIJIE ON sp.BYZD5=JJDM
		LEFT JOIN FJSX1 ON sp.FJSX1=fjsx1.SXDM
        WHERE v.RQ BETWEEN '{date_start}' AND '{date_end}'
        AND ISNULL(v.SP, '0')<>'1' AND d.LXMC='顾客订单'
        GROUP BY v.RQ, v.DJBH, d.LXMC, v.YXRQ, c.CKDM, c.CKMC, kh.PROVINCE,
                 kw.KWMC, v.BZ, sp.SPDM, sp.BYZD8, sp.BZSJ, v.DJ, v.ZY,
                 g1.GGDM, g1.GGMC, g2.GGDM, g2.GGMC, sp.BYZD4, dlmc,
                 sp.BYZD5, JJMC, sp.FJSX1, fjsx1.SXMC
        """.format(date_start=date_start, date_end=date_end)
        
        # 同步库存数据
        current_app.logger.info("开始同步库存数据...")
        stock_sql = """
        SELECT product_code , color_code, size_code, shop_code,
               shop_name, province, available_stock, unsold_days, turnover_days
        FROM a_ov_kdclsjy
        """.format(date=date_start)

        # 同步店铺基本信息
        current_app.logger.info("开始同步店铺基本信息...")
        shop_info_sql = """
        SELECT K.KHDM '店铺代码', K.KHMC '店铺', K.YXQYDM '区域代码', A.Q2 '大区', A.Q3 '小区',KHSX3.SXMC '商品负责人'
        FROM dbo.KEHU K
             INNER JOIN(SELECT Q3.YXQYDM, (CASE Q3.YXQYDM WHEN '028' THEN Q2.YXQYMC ELSE Q1.YXQYMC END) AS Q1, (CASE Q3.YXQYDM WHEN '028' THEN Q3.YXQYMC ELSE Q2.YXQYMC END) AS Q2, (CASE Q3.YXQYDM WHEN '028' THEN '' ELSE Q3.YXQYMC END) AS Q3
                        FROM YXQUYU AS Q3
                             LEFT JOIN YXQUYU AS Q2 ON Q3.SJQYDM=Q2.YXQYDM
                             LEFT JOIN YXQUYU AS Q1 ON Q2.SJQYDM=Q1.YXQYDM
                        GROUP BY Q3.YXQYDM, Q2.YXQYMC, Q1.YXQYMC, Q3.YXQYMC
                        HAVING(CASE Q3.YXQYDM WHEN '028' THEN Q2.YXQYMC ELSE Q1.YXQYMC END) IS NOT NULL AND(CASE Q3.YXQYDM WHEN '028' THEN Q3.YXQYMC ELSE Q2.YXQYMC END) IS NOT NULL) A ON K.YXQYDM=A.YXQYDM
	         LEFT JOIN khsx3 ON k.KHSX3=KHSX3.SXDM
        """
        
        # 执行同步
        with get_mssql_engine().connect() as conn:
            # 同步订单数据
            orders_df = pd.read_sql(text(order_sql), conn)
            current_app.logger.info(f"获取到 {len(orders_df)} 条订单数据")
            
            # 清理当天订单数据
            OrderSync.query.delete()
            current_app.logger.info("清理历史订单数据完成")
            
            # 插入新订单数据
            for _, row in orders_df.iterrows():
                order = OrderSync(
                    date=row['日期'],
                    order_no=row['订单号'],
                    shop_code=row['店铺代码'],
                    shop_name=row['店铺'],
                    province=row['省'],
                    product_code=row['商品代码'],
                    color_code=row['颜色代码'],
                    color_name=row['颜色'],
                    size_code=row['尺码代码'],
                    size_name=row['尺码'],
                    quantity=row['订单量'],
                    remark1=row['备注1'],
                    remark2=row['备注2'],
                    # 新增字段
                    year=row['年份'] if not pd.isna(row['年份']) else None,
                    category_code=row['大类代码'] if not pd.isna(row['大类代码']) else None,
                    category_name=row['大类'] if not pd.isna(row['大类']) else None,
                    season_code=row['季节代码'] if not pd.isna(row['季节代码']) else None,
                    season_name=row['季节'] if not pd.isna(row['季节']) else None,
                    subcategory_code=row['小类代码'] if not pd.isna(row['小类代码']) else None,
                    subcategory_name=row['小类'] if not pd.isna(row['小类']) else None,
                    remark=row['备注'] if not pd.isna(row['备注']) else None
                )
                db.session.add(order)
            
            # 同步库存数据
            stocks_df = pd.read_sql(text(stock_sql), conn)
            current_app.logger.info(f"获取到 {len(stocks_df)} 条库存数据")
            
            # 清理当天库存数据
            StockSync.query.delete()
            current_app.logger.info("清理历史库存数据完成")
            
            # 插入新库存数据
            for _, row in stocks_df.iterrows():
                # 检查必填字段
                if pd.isna(row['product_code']) or pd.isna(row['color_code']) or \
                   pd.isna(row['size_code']) or pd.isna(row['shop_code']):
                    continue
                    
                stock = StockSync(
                    date=date_start,
                    product_code=str(row['product_code']).strip(),  # 确保转为字符串并去除空格
                    color_code=str(row['color_code']).strip(),
                    size_code=str(row['size_code']).strip(),
                    shop_code=str(row['shop_code']).strip(),
                    shop_name=row['shop_name'] if not pd.isna(row['shop_name']) else None,
                    province=row['province'] if not pd.isna(row['province']) else None,
                    unsold_days=int(row['unsold_days']) if not pd.isna(row['unsold_days']) else 0,
                    turnover_days=int(row['turnover_days']) if not pd.isna(row['turnover_days']) else 0,
                    available_stock=int(row['available_stock']) if not pd.isna(row['available_stock']) else 0
                )
                db.session.add(stock)

            # 同步店铺基本信息
            shop_info_df = pd.read_sql(text(shop_info_sql), conn)
            current_app.logger.info(f"获取到 {len(shop_info_df)} 条店铺基本信息")

            # 清理店铺基本信息数据
            ShopInfo.query.delete()
            current_app.logger.info("清理历史店铺基本信息完成")

            # 插入新店铺基本信息
            for _, row in shop_info_df.iterrows():
                # 检查必填字段
                if pd.isna(row['店铺代码']):
                    continue

                shop_info = ShopInfo(
                    shop_code=str(row['店铺代码']).strip(),
                    shop_name=row['店铺'] if not pd.isna(row['店铺']) else None,
                    region_code=row['区域代码'] if not pd.isna(row['区域代码']) else None,
                    major_region=row['大区'] if not pd.isna(row['大区']) else None,
                    minor_region=row['小区'] if not pd.isna(row['小区']) else None,
                    product_manager=row['商品负责人'] if not pd.isna(row['商品负责人']) else None
                )
                db.session.add(shop_info)

            # 清理当天店铺调货统计
            ShopAllocationCount.query.filter_by(date=date_start).delete()
            # 清理当天调货计划
            AllocationPlan.query.filter_by(date=date_start).delete()
            
            # 初始化店铺调货统计
            shops = pd.unique(stocks_df['shop_code'])
            for shop_code in shops:
                count = ShopAllocationCount(
                    date=date_start,
                    shop_code=shop_code,
                    allocated_count=0
                )
                db.session.add(count)
            
            db.session.commit()
            
        current_app.logger.info("数据同步完成")
        return jsonify({
            'code': 0,
            'msg': '数据同步成功',
            'data': {
                'orders_count': len(orders_df),
                'stocks_count': len(stocks_df),
                'shop_info_count': len(shop_info_df)
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"数据同步失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'数据同步失败: {str(e)}'
        })

@ordermanage_api.route('/allocate', methods=['POST'])
def allocate():
    """执行调货分配"""
    try:
        date_range = request.json.get('date_range')
        try:
            date_start, date_end = parse_date_range(date_range)
        except ValueError as e:
            return jsonify({
                'code': 1,
                'msg': str(e)
            })
        
        # 清理日期范围内的分配方案
        AllocationPlan.query.filter(
            AllocationPlan.date.between(date_start, date_end)
        ).delete()
        
        # 获取所有待处理订单
        orders = OrderSync.query.filter(
            OrderSync.date.between(date_start, date_end)
        ).all()
        
        # 获取所有库存数据并转换为DataFrame
        stocks = StockSync.query.filter(
            StockSync.date.between(date_start, date_end)
        ).all()
        stocks_df = pd.DataFrame([{
            'product_code': s.product_code,
            'color_code': s.color_code,
            'size_code': s.size_code,
            'shop_code': s.shop_code,
            'shop_name': s.shop_name,
            'province': s.province,
            'available_stock': s.available_stock,
            'unsold_days': s.unsold_days,
            'turnover_days': s.turnover_days
        } for s in stocks])
        
        # 获取店铺调货统计
        shop_counts = {s.shop_code: s.allocated_count for s in 
                      ShopAllocationCount.query.filter(
                          ShopAllocationCount.date.between(date_start, date_end)
                      ).all()}
        
        current_app.logger.info(f"开始分配处理，订单数量：{len(orders)}")
        
        # 处理每个订单
        for order in orders:
            current_app.logger.info(f"\n处理订单：{order.order_no}")
            
            # 先检查总仓库存
            warehouse_stock = stocks_df[
                (stocks_df['shop_code'] == '000') &
                (stocks_df['product_code'] == order.product_code) &
                (stocks_df['color_code'] == order.color_code) &
                (stocks_df['size_code'] == order.size_code) &
                (stocks_df['available_stock'] >= order.quantity)
            ]
            
            if len(warehouse_stock) > 0:
                # 总仓有足够库存，直接分配
                current_app.logger.info(f"总仓库存充足，直接分配")
                warehouse = warehouse_stock.iloc[0]
                
                plan = AllocationPlan(
                    date=date_start,
                    order_no=order.order_no,
                    demand_shop_code=order.shop_code,
                    product_code=order.product_code,
                    color_code=order.color_code,
                    size_code=order.size_code,
                    quantity=order.quantity,
                    primary_shop_code='000',
                    primary_shop_name=warehouse['shop_name'],
                    status='allocated'
                )
                db.session.add(plan)
                
                # 更新总仓已分配数量
                shop_counts['000'] = shop_counts.get('000', 0) + 1
                
                # 更新总仓可用库存
                stocks_df.loc[
                    (stocks_df['shop_code'] == '000') &
                    (stocks_df['product_code'] == order.product_code) &
                    (stocks_df['color_code'] == order.color_code) &
                    (stocks_df['size_code'] == order.size_code),
                    'available_stock'
                ] -= order.quantity
                
                continue
            
            # 总仓库存不足，寻找店铺库存
            current_app.logger.info(f"总仓库存不足，寻找店铺库存")
            available_stocks = stocks_df[
                (stocks_df['shop_code'] != '000') &
                (stocks_df['product_code'] == order.product_code) &
                (stocks_df['color_code'] == order.color_code) &
                (stocks_df['size_code'] == order.size_code) &
                (stocks_df['available_stock'] >= order.quantity)
            ].copy()
            
            if len(available_stocks) == 0:
                current_app.logger.info(f"无可用库存，跳过")
                continue
            
            # 计算店铺已分配数量
            available_stocks['allocated_count'] = available_stocks['shop_code'].map(shop_counts)
            # 店铺限制最多分配5条
            available_stocks = available_stocks[available_stocks['allocated_count'] < 5]
            
            if len(available_stocks) == 0:
                current_app.logger.info(f"所有店铺已达到分配上限，跳过")
                continue
            
            # 计算评分
            available_stocks['same_province'] = (available_stocks['province'] == order.province).astype(int) * 30
            available_stocks['score'] = (
                available_stocks['unsold_days'] * 0.3 +  # 滞销天数 30%
                available_stocks['available_stock'] * 0.25 +  # 库存量 25%
                available_stocks['turnover_days'] * 0.1 +  # 周转天数 10%
                available_stocks['same_province'] +  # 同省加分 30%
                np.random.uniform(0, 5, len(available_stocks))  # 随机因子 10%
            )
            
            # 选择得分最高的两个店铺
            selected_shops = available_stocks.nlargest(2, 'score')
            
            if len(selected_shops) > 0:
                # 创建分配方案
                plan = AllocationPlan(
                    date=date_start,
                    order_no=order.order_no,
                    demand_shop_code=order.shop_code,
                    product_code=order.product_code,
                    color_code=order.color_code,
                    size_code=order.size_code,
                    quantity=order.quantity,
                    primary_shop_code=selected_shops.iloc[0]['shop_code'],
                    primary_shop_name=selected_shops.iloc[0]['shop_name'],
                    backup_shop_code=selected_shops.iloc[1]['shop_code'] if len(selected_shops) > 1 else None,
                    backup_shop_name=selected_shops.iloc[1]['shop_name'] if len(selected_shops) > 1 else None,
                    status='allocated'
                )
                db.session.add(plan)
                
                # 更新店铺已分配数量
                primary_shop = selected_shops.iloc[0]
                shop_counts[primary_shop['shop_code']] = shop_counts.get(primary_shop['shop_code'], 0) + 1
                
                # 更新可用库存
                stocks_df.loc[
                    (stocks_df['shop_code'] == primary_shop['shop_code']) &
                    (stocks_df['product_code'] == order.product_code) &
                    (stocks_df['color_code'] == order.color_code) &
                    (stocks_df['size_code'] == order.size_code),
                    'available_stock'
                ] -= order.quantity
        
        # 更新店铺调货统计
        for shop_code, allocated_count in shop_counts.items():
            count = ShopAllocationCount.query.filter(
                ShopAllocationCount.date.between(date_start, date_end),
                ShopAllocationCount.shop_code == shop_code
            ).first()
            if count:
                count.allocated_count = allocated_count
            else:
                count = ShopAllocationCount(
                    date=date_start,
                    shop_code=shop_code,
                    allocated_count=allocated_count
                )
                db.session.add(count)
        
        db.session.commit()
        
        return jsonify({
            'code': 0,
            'msg': '调货分配完成'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"调货分配失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'调货分配失败: {str(e)}'
        })

@ordermanage_api.route('/get_allocation_result', methods=['GET'])
def get_allocation_result():
    """获取分配结果"""
    try:
        date_range = request.args.get('date_range')
        try:
            date_start, date_end = parse_date_range(date_range)
        except ValueError as e:
            return jsonify({
                'code': 1,
                'msg': str(e)
            })
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 30, type=int)
        
        # 获取订单数据
        query = OrderSync.query.filter(
            OrderSync.date.between(date_start, date_end)
        )
        total = query.count()
        
        # 获取这些订单的分配方案
        order_nos = [order.order_no for order in query.all()]
        allocation_plans = {
            (plan.order_no, plan.product_code, plan.color_code, plan.size_code): plan 
            for plan in AllocationPlan.query.filter(
                AllocationPlan.date.between(date_start, date_end),
                AllocationPlan.order_no.in_(order_nos)
            ).all()
        }
        
        # 组装数据并排序
        data = []
        for order in query.all():
            plan = allocation_plans.get((order.order_no, order.product_code, order.color_code, order.size_code))
            item = {
                'date': order.date.strftime('%Y-%m-%d'),
                'order_no': order.order_no,
                'shop_code': order.shop_code,
                'shop_name': order.shop_name,
                'province': order.province,
                'product_code': order.product_code,
                'color_code': order.color_code,
                'color_name': order.color_name,
                'size_code': order.size_code,
                'size_name': order.size_name,
                'quantity': order.quantity,
                'remark1': order.remark1,
                'remark2': order.remark2,
                'primary_shop_code': None,
                'primary_shop_name': None,
                'backup_shop_code': None,
                'backup_shop_name': None,
                'status': 'pending'
            }
            
            if plan:
                item.update({
                    'primary_shop_code': plan.primary_shop_code,
                    'primary_shop_name': plan.primary_shop_name,
                    'backup_shop_code': plan.backup_shop_code,
                    'backup_shop_name': plan.backup_shop_name,
                    'status': plan.status
                })
            
            data.append(item)
        
        # 排序逻辑
        def sort_key(x):
            # 状态排序值
            status_order = {'allocated': 0, 'manual_allocated': 1, 'pending': 2}
            return (
                status_order.get(x['status'], 3),  # 状态排序
                x['product_code'],                 # 商品代码
                x['color_code'],                   # 颜色代码
                x['size_code'],                    # 尺码
                x['primary_shop_name'] or ''       # 主要调出店铺
            )
        
        # 按多个字段排序
        data.sort(key=sort_key)
        
        # 分页
        start = (page - 1) * limit
        end = start + limit
        paginated_data = data[start:end]
        
        return jsonify({
            'code': 0,
            'msg': '获取成功',
            'count': total,
            'data': paginated_data
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分配结果失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'获取分配结果失败: {str(e)}'
        })

@ordermanage_api.route('/export_excel', methods=['GET'])
def export_excel():
    """导出Excel"""
    try:
        date_range = request.args.get('date_range')
        try:
            date_start, date_end = parse_date_range(date_range)
        except ValueError as e:
            return jsonify({
                'code': 1,
                'msg': str(e)
            })

        # 获取所有订单
        orders = OrderSync.query.filter(OrderSync.date.between(date_start, date_end)).all()

        # 获取分配方案
        allocation_plans = {
            (plan.order_no, plan.product_code, plan.color_code, plan.size_code): plan
            for plan in AllocationPlan.query.filter(
                AllocationPlan.date.between(date_start, date_end)
            ).all()
        }

        # 获取店铺基本信息
        shop_infos = {
            shop.shop_code: shop
            for shop in ShopInfo.query.all()
        }

        # 定义折扣计算函数
        def get_discount(shop_name):
            if shop_name and len(shop_name) >= 2 and shop_name[:2] == 'JM':
                return 0.7
            return 0.3

        # 准备Excel数据
        excel_data = []
        for order in orders:
            plan = allocation_plans.get((order.order_no, order.product_code, order.color_code, order.size_code))
            status = '已分配' if plan and plan.status == 'allocated' else \
                     '手动分配' if plan and plan.status == 'manual_allocated' else '待分配'

            # 获取店铺信息
            shop_info = shop_infos.get(order.shop_code, None)

            # 计算折扣
            in_shop_discount = get_discount(order.shop_name)
            out_shop_discount = get_discount(plan.primary_shop_name if plan else '')

            excel_data.append({
                '日期': order.date.strftime('%Y-%m-%d'),
                '订单号': order.order_no,
                '调入大区': shop_info.major_region if shop_info else '',
                '调入小区': shop_info.minor_region if shop_info else '',
                '类型名称': '顾客订单',
                '调入商店代码': order.shop_code,
                '调入商店名称': order.shop_name,
                '年份': order.year or '',
                '季节名称': order.season_name or '',
                '大类名称': order.category_name or '',
                '小类名称': order.subcategory_name or '',
                '商品代码': order.product_code,
                '颜色代码': order.color_code,
                '颜色名称': order.color_name,
                '尺码代码': order.size_code,
                '尺码名称': order.size_name,
                '数量': order.quantity,
                '主要调出店铺代码': plan.primary_shop_code if plan else '',
                '主要调出店铺': plan.primary_shop_name if plan else '',
                '备用调出店铺代码': plan.backup_shop_code if plan else '',
                '备用调出店铺': plan.backup_shop_name if plan else '',
                '调入店商品负责人': shop_info.product_manager if shop_info else '',
                '调入店折扣': in_shop_discount,
                '调出店折扣': out_shop_discount,
                '备注': order.remark or '',
                '状态': status
            })

        # 对数据进行排序，将"待分配"的记录放到最后
        df = pd.DataFrame(excel_data)
        df['状态排序'] = df['状态'].map({'已分配': 0, '手动分配': 1, '待分配': 2})
        df = df.sort_values('状态排序')
        df = df.drop('状态排序', axis=1)  # 删除辅助排序列

        # 创建DataFrame并导出到Excel
        excel_path = os.path.join('exceldata', 'order_export_excel', f'{date_start}至{date_end}订单分配结果.xlsx')

        # 使用 xlsxwriter 引擎，它支持更好的中文处理
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='调货分配结果')

            # 获取 xlsxwriter 工作簿和工作表对象
            workbook = writer.book
            worksheet = writer.sheets['调货分配结果']

            # 设置列宽
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(str(col))
                )
                worksheet.set_column(idx, idx, max_length)

        return jsonify({
            'code': 0,
            'msg': '导出成功',
            'data': {
                'file_name': f'{date_start}至{date_end}订单分配结果.xlsx',
                'file_path': f'/api/v1/ordermanage/download_excel/{date_start}至{date_end}订单分配结果.xlsx'
            }
        })

    except Exception as e:
        current_app.logger.error(f"导出Excel失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'导出Excel失败: {str(e)}'
        })

@ordermanage_api.route('/update_allocation', methods=['POST'])
def update_allocation():
    """手动更新调货方案"""
    try:
        data = request.json
        date_range = data.get('date_range')
        try:
            date_start, date_end = parse_date_range(date_range)
        except ValueError as e:
            return jsonify({
                'code': 1,
                'msg': str(e)
            })
            
        order_no = data.get('order_no')
        primary_shop_code = data.get('primary_shop_code')
        primary_shop_name = data.get('primary_shop_name')
        backup_shop_code = data.get('backup_shop_code')
        backup_shop_name = data.get('backup_shop_name')
        
        # 获取原方案
        plan = AllocationPlan.query.filter(
            AllocationPlan.date.between(date_start, date_end),
            AllocationPlan.order_no == order_no
        ).first()
        
        if not plan:
            # 如果没有方案，创建新方案
            order = OrderSync.query.filter(
                OrderSync.date.between(date_start, date_end),
                OrderSync.order_no == order_no
            ).first()
            
            if not order:
                raise Exception("订单不存在")
                
            plan = AllocationPlan(
                date=order.date,
                order_no=order_no,
                product_code=order.product_code,
                color_code=order.color_code,
                size_code=order.size_code,
                primary_shop_code=primary_shop_code,
                primary_shop_name=primary_shop_name,
                backup_shop_code=backup_shop_code,
                backup_shop_name=backup_shop_name,
                status='manual_allocated'
            )
            db.session.add(plan)
        else:
            # 更新现有方案
            plan.primary_shop_code = primary_shop_code
            plan.primary_shop_name = primary_shop_name
            plan.backup_shop_code = backup_shop_code
            plan.backup_shop_name = backup_shop_name
            plan.status = 'manual_allocated'
        
        db.session.commit()
        
        return jsonify({
            'code': 0,
            'msg': '更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新调货方案失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'更新失败: {str(e)}'
        })

@ordermanage_api.route('/get_available_shops', methods=['GET'])
def get_available_shops():
    """获取可用店铺列表"""
    try:
        date_range = request.args.get('date_range')
        try:
            date_start, date_end = parse_date_range(date_range)
        except ValueError as e:
            return jsonify({
                'code': 1,
                'msg': str(e)
            })
            
        product_code = request.args.get('product_code')
        color_code = request.args.get('color_code')
        size_code = request.args.get('size_code')
        quantity = request.args.get('quantity', type=int)
        
        # 获取有库存的店铺
        stocks = StockSync.query.filter(
            StockSync.date.between(date_start, date_end),
            StockSync.product_code == product_code,
            StockSync.color_code == color_code,
            StockSync.size_code == size_code,
            StockSync.available_stock > 0
        ).all()
        
        # 获取店铺已分配数量
        shop_counts = {
            count.shop_code: count.allocated_count
            for count in ShopAllocationCount.query.filter(
                ShopAllocationCount.date.between(date_start, date_end)
            ).all()
        }
        
        # 转换为列表
        result = []
        for stock in stocks:
            # 总仓不受5条限制
            if stock.shop_code == '000' or shop_counts.get(stock.shop_code, 0) < 5:
                result.append({
                    'shop_code': stock.shop_code,
                    'shop_name': '总部' if stock.shop_code == '000' else stock.shop_name,
                    'available_stock': stock.available_stock,
                    'allocated_count': shop_counts.get(stock.shop_code, 0),
                    'unsold_days': stock.unsold_days
                })
        
        return jsonify({
            'code': 0,
            'msg': '获取成功',
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f"获取可用店铺失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'获取可用店铺失败: {str(e)}'
        })

@ordermanage_api.route('/download_excel/<filename>', methods=['GET'])
def download_excel(filename):
    """下载Excel文件"""
    try:
        file_path = os.path.join('exceldata', 'order_export_excel', filename)
        if os.path.exists(file_path):
            return send_file(
                file_path,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
        else:
            return jsonify({
                'code': 1,
                'msg': '文件不存在'
            })
    except Exception as e:
        current_app.logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'下载文件失败: {str(e)}'
        })

@ordermanage_api.route('/update_data', methods=['POST'])
def update_data():
    """清空数据并重新生成"""
    try:
        date_range = request.json.get('date_range')
        try:
            date_start, date_end = parse_date_range(date_range)
        except ValueError as e:
            return jsonify({
                'code': 1,
                'msg': str(e)
            })
            
        current_app.logger.info(f"开始清空并重新生成数据，日期范围：{date_start} 至 {date_end}")
        
        # 先执行存储过程生成数据
        with get_mssql_engine().begin() as conn:
            # 确保存储过程在事务中执行并提交
            current_app.logger.info("准备执行存储过程 PROC_OV_KDPCLSJY...")
            # 修改存储过程调用，传入日期参数
            conn.execute(text(
                "EXEC PROC_OV_KDPCLSJY @date_str=:date_start, @date_end=:date_end"
            ), {
                'date_start': date_start,
                'date_end': date_end
            })
            current_app.logger.info("存储过程执行完成")
            
            # 验证表是否创建成功
            result = conn.execute(text("""
                SELECT OBJECT_ID('dbo.a_ov_kdclsjy') as table_exists
            """))
            table_exists = result.scalar()
            
            if not table_exists:
                raise Exception("表 a_ov_kdclsjy 未能成功创建")
        
        # 清空并重新生成本地数据
        with db.session.begin():
            # 清空日期范围内的所有相关表数据
            # OrderSync.query.filter(
            #     OrderSync.date.between(date_start, date_end)
            # ).delete()
            OrderSync.query.delete()
            current_app.logger.info("订单数据已清空")
            
            # StockSync.query.filter(
            #     StockSync.date.between(date_start, date_end)
            # ).delete()
            StockSync.query.delete()
            current_app.logger.info("库存数据已清空")
            
            # AllocationPlan.query.filter(
            #     AllocationPlan.date.between(date_start, date_end)
            # ).delete()
            AllocationPlan.query.delete()
            current_app.logger.info("分配方案已清空")
            

            # ShopAllocationCount.query.filter(
            #     ShopAllocationCount.date.between(date_start, date_end)
            # ).delete()
            ShopAllocationCount.query.delete()
            current_app.logger.info("店铺调货统计已清空")
        
        return jsonify({
            'code': 0,
            'msg': f'数据清空并重新生成成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"数据操作失败: {str(e)}")
        current_app.logger.error(f"错误类型: {type(e).__name__}")
        current_app.logger.error(f"错误详情: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f'数据操作失败: {str(e)}'
        })

# @ordermanage_api.route('/generate_data', methods=['POST'])
# def generate_data():
    # """执行存储过程生成数据"""
    # try:
        
    #     # 创建一个独立的数据库连接和事务
    #     with get_mssql_engine().begin() as conn:
    #         # 确保存储过程在事务中执行并提交
    #         current_app.logger.info("准备执行存储过程 PROC_OV_KDPCLSJY...")
    #         conn.execute(text("EXEC PROC_OV_KDPCLSJY"))
    #         current_app.logger.info("存储过程执行完成")
            
    #         # 验证表是否创建成功
    #         result = conn.execute(text("""
    #             SELECT OBJECT_ID('dbo.a_ov_kdclsjy') as table_exists
    #         """))
    #         table_exists = result.scalar()
            
    #         if not table_exists:
    #             raise Exception("表 a_ov_kdclsjy 未能成功创建")
            
    #         # 查询生成的数据
    #         result = conn.execute(text("""
    #             SELECT product_code, color_code, size_code, shop_code, 
    #                    shop_name, province, available_stock, unsold_days, turnover_days 
    #             FROM dbo.a_ov_kdclsjy
    #         """))
    #         stocks_data = result.fetchall()
    #         total_rows = len(stocks_data)
    #         current_app.logger.info(f"获取到 {total_rows} 条记录")
            
        
    #     return jsonify({
    #         'code': 0,
    #         'msg': f'数据生成成功，共生成 {total_rows} 条记录'
    #     })
        
    # except Exception as e:
    #     current_app.logger.error(f"数据生成失败: {str(e)}")
    #     current_app.logger.error(f"错误类型: {type(e).__name__}")
    #     current_app.logger.error(f"错误详情: {str(e)}")
    #     return jsonify({
    #         'code': 1,
    #         'msg': f'数据生成失败: {str(e)}'
    #     })
