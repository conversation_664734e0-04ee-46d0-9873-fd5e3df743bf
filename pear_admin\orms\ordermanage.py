from pear_admin.extensions import db
from ._base import BaseORM
from sqlalchemy import Index, Column, Integer, String, Date, DateTime, Text, func

class OrderSync(BaseORM):
    """客订同步表"""
    __tablename__ = 'ordermanage_ordersync'

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False, comment='日期')
    order_no = Column(String(50), nullable=False, comment='订单号')
    shop_code = Column(String(20), nullable=False, comment='店铺代码')
    shop_name = Column(String(50), comment='店铺名称')
    province = Column(String(20), comment='省份')
    product_code = Column(String(20), nullable=False, comment='商品代码')
    color_code = Column(String(20), nullable=False, comment='颜色代码')
    color_name = Column(String(50), comment='颜色名称')
    size_code = Column(String(20), nullable=False, comment='尺码代码')
    size_name = Column(String(50), comment='尺码名称')
    quantity = Column(Integer, nullable=False, default=0, comment='订单量')
    remark1 = Column(String(500), comment='备注1')
    remark2 = Column(String(500), comment='备注2')
    # 新增字段
    year = Column(String(10), comment='年份')
    category_code = Column(String(20), comment='大类代码')
    category_name = Column(String(50), comment='大类')
    season_code = Column(String(20), comment='季节代码')
    season_name = Column(String(50), comment='季节')
    subcategory_code = Column(String(20), comment='小类代码')
    subcategory_name = Column(String(50), comment='小类')
    remark = Column(String(500), comment='备注')
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')

    __table_args__ = (
        Index('idx_order_date', 'date'),
        Index('idx_order_no', 'order_no'),
    )

class StockSync(BaseORM):
    """库存同步表"""
    __tablename__ = 'ordermanage_stocksync'
    
    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False, comment='日期')
    product_code = Column(String(20), nullable=False, comment='商品代码')
    color_code = Column(String(20), nullable=False, comment='颜色代码')
    size_code = Column(String(20), nullable=False, comment='尺码代码')
    shop_code = Column(String(20), nullable=False, comment='商店代码')
    shop_name = Column(String(50), comment='店铺名称')
    province = Column(String(20), comment='省份')
    unsold_days = Column(Integer, default=0, comment='滞销天数')
    turnover_days = Column(Integer, default=0, comment='单款周转')
    available_stock = Column(Integer, default=0, comment='可用库存')
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')

    __table_args__ = (
        Index('idx_stock_date', 'date'),
        Index('idx_stock_product', 'product_code', 'color_code', 'size_code'),
    )

class AllocationPlan(BaseORM):
    """调货方案表"""
    __tablename__ = 'ordermanage_allocationplan'
    
    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False, comment='日期')
    order_no = Column(String(50), nullable=False, comment='订单号')
    demand_shop_code = Column(String(20), nullable=False, comment='需求店铺代码')
    primary_shop_code = Column(String(20), comment='主要调出店铺代码')
    primary_shop_name = Column(String(50), comment='主要调出店铺名称')
    backup_shop_code = Column(String(20), comment='备用调出店铺代码')
    backup_shop_name = Column(String(50), comment='备用调出店铺名称')
    product_code = Column(String(20), nullable=False, comment='商品代码')
    color_code = Column(String(20), nullable=False, comment='颜色代码')
    size_code = Column(String(20), nullable=False, comment='尺码代码')
    quantity = Column(Integer, nullable=False, default=0, comment='调货数量')
    status = Column(String(20), default='pending', comment='状态')
    is_manual = Column(Integer, default=0, comment='是否手动调整')
    remark = Column(Text, comment='备注')
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, onupdate=func.now(), comment='更新时间')

    __table_args__ = (
        Index('idx_plan_date', 'date'),
        Index('idx_plan_order', 'order_no'),
    )

class ShopAllocationCount(BaseORM):
    """店铺调货统计表"""
    __tablename__ = 'ordermanage_shopallocationcount'
    
    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False, comment='日期')
    shop_code = Column(String(20), nullable=False, comment='店铺代码')
    allocated_count = Column(Integer, default=0, comment='已调出数量')
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, onupdate=func.now(), comment='更新时间')

    __table_args__ = (
        Index('idx_shop_date', 'date', 'shop_code', unique=True),
    )

class ShopInfo(BaseORM):
    """店铺基本信息表"""
    __tablename__ = 'ordermanage_shopinfo'

    id = Column(Integer, primary_key=True)
    shop_code = Column(String(20), nullable=False, comment='店铺代码')
    shop_name = Column(String(50), comment='店铺名称')
    region_code = Column(String(20), comment='区域代码')
    major_region = Column(String(50), comment='大区')
    minor_region = Column(String(50), comment='小区')
    product_manager = Column(String(50), comment='商品负责人')
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, onupdate=func.now(), comment='更新时间')

    __table_args__ = (
        Index('idx_shop_code', 'shop_code', unique=True),
    )